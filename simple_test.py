#!/usr/bin/env python3
"""
简单的DeepSeek测试脚本
验证环境是否正确配置
"""

import sys
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM

def main():
    print("🧪 DeepSeek 环境测试")
    print("=" * 40)
    
    # 检查PyTorch
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    
    if torch.cuda.is_available():
        print(f"GPU: {torch.cuda.get_device_name(0)}")
        print(f"显存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
    
    print("\n📥 尝试加载小型模型进行测试...")
    
    # 使用一个很小的模型进行测试
    model_name = "microsoft/DialoGPT-small"  # 使用一个小的对话模型作为测试
    
    try:
        print("1. 加载tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        print("✅ Tokenizer加载成功")
        
        print("2. 加载模型...")
        model = AutoModelForCausalLM.from_pretrained(model_name)
        print("✅ 模型加载成功")
        
        print("3. 测试生成...")
        # 简单的生成测试
        input_text = "Hello, how are you?"
        inputs = tokenizer.encode(input_text, return_tensors="pt")
        
        with torch.no_grad():
            outputs = model.generate(
                inputs,
                max_length=50,
                num_return_sequences=1,
                temperature=0.7,
                pad_token_id=tokenizer.eos_token_id
            )
        
        response = tokenizer.decode(outputs[0], skip_special_tokens=True)
        print(f"✅ 生成测试成功: {response}")
        
        print("\n🎉 环境测试通过！可以开始使用DeepSeek模型了。")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        print("💡 请检查网络连接和依赖安装")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🚀 现在可以运行以下命令启动DeepSeek:")
        print("   py chat.py          # 命令行聊天")
        print("   py web_interface.py # Web界面")
    else:
        print("\n❌ 环境配置有问题，请检查依赖安装")
