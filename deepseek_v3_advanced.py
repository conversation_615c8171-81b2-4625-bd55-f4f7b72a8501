#!/usr/bin/env python3
"""
DeepSeek V3 高级部署脚本
支持多种推理框架：SGLang, vLLM, Transformers
针对RTX 3090优化
"""

import os
import sys
import argparse
import torch
import subprocess
import json
from pathlib import Path

def check_gpu():
    """检查GPU配置"""
    if not torch.cuda.is_available():
        print("❌ 未检测到CUDA GPU")
        return False
    
    gpu_name = torch.cuda.get_device_name(0)
    gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
    
    print(f"🎮 GPU: {gpu_name}")
    print(f"💾 显存: {gpu_memory:.1f} GB")
    
    if gpu_memory < 20:
        print("⚠️  显存可能不足，建议使用量化模型")
    
    return True

def install_dependencies(framework):
    """安装依赖"""
    print(f"📦 安装 {framework} 依赖...")
    
    if framework == "sglang":
        commands = [
            "pip install sglang[all]",
            "pip install flashinfer -i https://flashinfer.ai/whl/cu121/torch2.4/"
        ]
    elif framework == "vllm":
        commands = [
            "pip install vllm",
            "pip install ray"
        ]
    elif framework == "transformers":
        commands = [
            "pip install transformers>=4.36.0",
            "pip install bitsandbytes",
            "pip install accelerate"
        ]
    
    for cmd in commands:
        try:
            subprocess.run(cmd.split(), check=True)
            print(f"✅ {cmd} 安装成功")
        except subprocess.CalledProcessError as e:
            print(f"❌ {cmd} 安装失败: {e}")
            return False
    
    return True

def run_sglang_server():
    """启动SGLang服务器"""
    print("🚀 启动SGLang服务器...")
    
    # SGLang启动命令
    cmd = [
        "python", "-m", "sglang.launch_server",
        "--model-path", "deepseek-ai/DeepSeek-V3",
        "--host", "0.0.0.0",
        "--port", "30000",
        "--tp", "1",  # 单GPU
        "--quantization", "fp8",  # 使用FP8量化
        "--trust-remote-code"
    ]
    
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        process = subprocess.Popen(cmd)
        print("✅ SGLang服务器启动成功!")
        print("🌐 服务地址: http://localhost:30000")
        print("📖 API文档: http://localhost:30000/docs")
        return process
    except Exception as e:
        print(f"❌ SGLang启动失败: {e}")
        return None

def run_vllm_server():
    """启动vLLM服务器"""
    print("🚀 启动vLLM服务器...")
    
    cmd = [
        "python", "-m", "vllm.entrypoints.openai.api_server",
        "--model", "deepseek-ai/DeepSeek-V3",
        "--host", "0.0.0.0",
        "--port", "8000",
        "--tensor-parallel-size", "1",
        "--quantization", "fp8",
        "--trust-remote-code",
        "--max-model-len", "8192"  # 限制序列长度以节省显存
    ]
    
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        process = subprocess.Popen(cmd)
        print("✅ vLLM服务器启动成功!")
        print("🌐 服务地址: http://localhost:8000")
        print("📖 API文档: http://localhost:8000/docs")
        return process
    except Exception as e:
        print(f"❌ vLLM启动失败: {e}")
        return None

def create_client_script():
    """创建客户端测试脚本"""
    client_code = '''
import requests
import json

def test_api(base_url, prompt):
    """测试API"""
    url = f"{base_url}/v1/chat/completions"
    
    data = {
        "model": "deepseek-ai/DeepSeek-V3",
        "messages": [
            {"role": "user", "content": prompt}
        ],
        "temperature": 0.7,
        "max_tokens": 512
    }
    
    try:
        response = requests.post(url, json=data, timeout=60)
        response.raise_for_status()
        
        result = response.json()
        return result["choices"][0]["message"]["content"]
    except Exception as e:
        return f"错误: {e}"

if __name__ == "__main__":
    # 测试不同的服务
    servers = [
        ("SGLang", "http://localhost:30000"),
        ("vLLM", "http://localhost:8000")
    ]
    
    prompt = "请用中文介绍一下DeepSeek V3模型的特点"
    
    for name, url in servers:
        print(f"\\n🧪 测试 {name} 服务...")
        try:
            response = test_api(url, prompt)
            print(f"✅ {name} 响应: {response[:200]}...")
        except Exception as e:
            print(f"❌ {name} 测试失败: {e}")
'''
    
    with open("test_client.py", "w", encoding="utf-8") as f:
        f.write(client_code)
    
    print("✅ 客户端测试脚本已创建: test_client.py")

def main():
    parser = argparse.ArgumentParser(description="DeepSeek V3 高级部署工具")
    parser.add_argument("--framework", choices=["sglang", "vllm", "transformers"], 
                       default="sglang", help="选择推理框架")
    parser.add_argument("--install-deps", action="store_true", help="安装依赖")
    parser.add_argument("--server-only", action="store_true", help="仅启动服务器")
    
    args = parser.parse_args()
    
    print("🚀 DeepSeek V3 高级部署工具")
    print("=" * 50)
    
    # 检查GPU
    if not check_gpu():
        print("⚠️  建议在有GPU的环境中运行以获得最佳性能")
    
    # 安装依赖
    if args.install_deps:
        if not install_dependencies(args.framework):
            print("❌ 依赖安装失败")
            return
    
    # 创建客户端脚本
    create_client_script()
    
    # 启动服务器
    if args.framework == "sglang":
        process = run_sglang_server()
    elif args.framework == "vllm":
        process = run_vllm_server()
    elif args.framework == "transformers":
        print("🔄 使用Transformers框架，请运行 python chat.py")
        return
    
    if process and not args.server_only:
        try:
            print("\n⏳ 等待服务器启动...")
            print("💡 服务器启动后，可以运行 'python test_client.py' 测试API")
            print("🛑 按 Ctrl+C 停止服务器")
            process.wait()
        except KeyboardInterrupt:
            print("\n🛑 正在停止服务器...")
            process.terminate()
            process.wait()
            print("✅ 服务器已停止")

if __name__ == "__main__":
    main()
