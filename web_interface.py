#!/usr/bin/env python3
"""
DeepSeek V3 Web界面
使用Gradio创建简单的聊天界面
"""

import gradio as gr
import torch
import gc
import time
from transformers import AutoModelForCausalLM, AutoTokenizer, BitsAndBytesConfig
import threading
import queue

class DeepSeekV3Chat:
    def __init__(self):
        self.model = None
        self.tokenizer = None
        self.model_name = None
        self.is_loading = False
        
    def load_model(self, model_choice="deepseek-v3", use_quantization=True):
        """加载模型"""
        if self.is_loading:
            return "⏳ 模型正在加载中，请稍候..."
        
        self.is_loading = True
        
        try:
            # 选择模型
            if model_choice == "deepseek-v3":
                self.model_name = "deepseek-ai/DeepSeek-V3"
            elif model_choice == "deepseek-coder-6.7b":
                self.model_name = "deepseek-ai/deepseek-coder-6.7b-instruct"
            elif model_choice == "deepseek-coder-33b":
                self.model_name = "deepseek-ai/deepseek-coder-33b-instruct"
            
            # 配置量化
            quantization_config = None
            if use_quantization:
                quantization_config = BitsAndBytesConfig(
                    load_in_4bit=True,
                    bnb_4bit_compute_dtype=torch.float16,
                    bnb_4bit_use_double_quant=True,
                    bnb_4bit_quant_type="nf4"
                )
            
            # 加载tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.model_name,
                trust_remote_code=True,
                cache_dir="./model_cache"
            )
            
            # 加载模型
            self.model = AutoModelForCausalLM.from_pretrained(
                self.model_name,
                quantization_config=quantization_config,
                device_map="auto",
                trust_remote_code=True,
                torch_dtype=torch.float16,
                cache_dir="./model_cache",
                low_cpu_mem_usage=True
            )
            
            # 清理内存
            gc.collect()
            torch.cuda.empty_cache()
            
            self.is_loading = False
            return f"✅ 模型 {self.model_name} 加载成功！"
            
        except Exception as e:
            self.is_loading = False
            return f"❌ 模型加载失败: {str(e)}"
    
    def generate_response(self, message, history, temperature=0.7, max_tokens=512):
        """生成回答"""
        if self.model is None or self.tokenizer is None:
            return "❌ 请先加载模型", history
        
        try:
            # 构建对话历史
            messages = []
            for user_msg, assistant_msg in history:
                messages.append({"role": "user", "content": user_msg})
                messages.append({"role": "assistant", "content": assistant_msg})
            messages.append({"role": "user", "content": message})
            
            # 使用chat template
            if hasattr(self.tokenizer, 'apply_chat_template'):
                input_text = self.tokenizer.apply_chat_template(
                    messages, tokenize=False, add_generation_prompt=True
                )
            else:
                input_text = f"User: {message}\n\nAssistant:"
            
            # 生成回答
            inputs = self.tokenizer(
                input_text, 
                return_tensors="pt", 
                truncation=True, 
                max_length=2048
            )
            
            if torch.cuda.is_available():
                inputs = {k: v.to(self.model.device) for k, v in inputs.items()}
            
            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    max_new_tokens=max_tokens,
                    temperature=temperature,
                    top_p=0.9,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id,
                    repetition_penalty=1.1
                )
            
            # 解码回答
            response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            # 提取新生成的部分
            if input_text in response:
                response = response.replace(input_text, "").strip()
            
            # 清理内存
            del inputs, outputs
            torch.cuda.empty_cache()
            
            # 更新历史
            history.append((message, response))
            
            return "", history
            
        except Exception as e:
            error_msg = f"生成回答时出错: {str(e)}"
            history.append((message, error_msg))
            return "", history
    
    def get_system_info(self):
        """获取系统信息"""
        info = []
        info.append(f"🔧 当前模型: {self.model_name or '未加载'}")
        
        if torch.cuda.is_available():
            gpu_name = torch.cuda.get_device_name(0)
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
            used_memory = torch.cuda.memory_allocated() / 1024**3
            
            info.append(f"🎮 GPU: {gpu_name}")
            info.append(f"💾 显存: {used_memory:.2f} GB / {gpu_memory:.1f} GB")
        else:
            info.append("💻 运行在CPU上")
        
        return "\n".join(info)

# 创建聊天实例
chat_bot = DeepSeekV3Chat()

# 创建Gradio界面
def create_interface():
    with gr.Blocks(title="DeepSeek V3 Chat", theme=gr.themes.Soft()) as demo:
        gr.Markdown("# 🤖 DeepSeek V3 本地聊天界面")
        
        with gr.Row():
            with gr.Column(scale=3):
                # 聊天界面
                chatbot = gr.Chatbot(
                    height=500,
                    label="DeepSeek V3 对话",
                    show_copy_button=True
                )
                
                with gr.Row():
                    msg = gr.Textbox(
                        placeholder="输入你的问题...",
                        label="消息",
                        scale=4
                    )
                    send_btn = gr.Button("发送", variant="primary", scale=1)
                
                with gr.Row():
                    clear_btn = gr.Button("清空对话")
                    retry_btn = gr.Button("重新生成")
            
            with gr.Column(scale=1):
                # 控制面板
                gr.Markdown("## ⚙️ 控制面板")
                
                model_choice = gr.Dropdown(
                    choices=[
                        ("DeepSeek V3 (推荐)", "deepseek-v3"),
                        ("DeepSeek Coder 6.7B", "deepseek-coder-6.7b"),
                        ("DeepSeek Coder 33B", "deepseek-coder-33b")
                    ],
                    value="deepseek-v3",
                    label="选择模型"
                )
                
                use_quantization = gr.Checkbox(
                    value=True,
                    label="使用4bit量化 (推荐)"
                )
                
                load_btn = gr.Button("加载模型", variant="secondary")
                load_status = gr.Textbox(label="加载状态", interactive=False)
                
                gr.Markdown("### 生成参数")
                temperature = gr.Slider(
                    minimum=0.1,
                    maximum=2.0,
                    value=0.7,
                    step=0.1,
                    label="Temperature"
                )
                
                max_tokens = gr.Slider(
                    minimum=50,
                    maximum=1024,
                    value=512,
                    step=50,
                    label="最大生成长度"
                )
                
                gr.Markdown("### 系统信息")
                system_info = gr.Textbox(
                    label="系统状态",
                    interactive=False,
                    lines=4
                )
                
                refresh_btn = gr.Button("刷新信息")
        
        # 事件绑定
        def respond(message, history, temp, max_tok):
            return chat_bot.generate_response(message, history, temp, max_tok)
        
        msg.submit(respond, [msg, chatbot, temperature, max_tokens], [msg, chatbot])
        send_btn.click(respond, [msg, chatbot, temperature, max_tokens], [msg, chatbot])
        
        clear_btn.click(lambda: [], None, chatbot)
        
        load_btn.click(
            chat_bot.load_model,
            [model_choice, use_quantization],
            load_status
        )
        
        refresh_btn.click(chat_bot.get_system_info, None, system_info)
        
        # 初始化系统信息
        demo.load(chat_bot.get_system_info, None, system_info)
    
    return demo

if __name__ == "__main__":
    # 检查Gradio是否安装
    try:
        import gradio as gr
    except ImportError:
        print("❌ 请先安装Gradio: pip install gradio")
        exit(1)
    
    print("🚀 启动DeepSeek V3 Web界面...")
    demo = create_interface()
    demo.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        show_error=True
    )
