from transformers import AutoModelForCausalLM, AutoTokenizer, logging, BitsAndBytesConfig
import torch
import os
import sys
import gc

# 设置详细的日志
logging.set_verbosity_info()
logging.enable_explicit_format()

print("🚀 DeepSeek V3 本地部署脚本")
print("=" * 50)
print(f"缓存目录: {os.path.expanduser('~/.cache/huggingface')}")

# 检查GPU信息
if torch.cuda.is_available():
    gpu_name = torch.cuda.get_device_name(0)
    gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
    print(f"🎮 检测到GPU: {gpu_name}")
    print(f"💾 GPU显存: {gpu_memory:.1f} GB")
else:
    print("❌ 未检测到CUDA GPU，将使用CPU运行（速度较慢）")

# 配置量化参数以适配RTX 3090
print("\n⚙️ 配置量化参数...")
quantization_config = BitsAndBytesConfig(
    load_in_4bit=True,
    bnb_4bit_compute_dtype=torch.float16,
    bnb_4bit_use_double_quant=True,
    bnb_4bit_quant_type="nf4"
)

# 初始化模型和分词器 - 使用DeepSeek V3
model_name = "deepseek-ai/DeepSeek-V3"
print(f"\n📥 正在加载模型: {model_name}")
print("⚠️  注意: DeepSeek V3是一个大型模型，首次下载可能需要较长时间...")

try:
    print("\n1. 正在加载tokenizer...")
    tokenizer = AutoTokenizer.from_pretrained(
        model_name,
        trust_remote_code=True,
        cache_dir="./model_cache"
    )
    print("✅ Tokenizer加载完成！")

    print("\n2. 正在加载模型（使用4bit量化）...")
    model = AutoModelForCausalLM.from_pretrained(
        model_name,
        quantization_config=quantization_config,
        device_map="auto",
        trust_remote_code=True,
        torch_dtype=torch.float16,
        cache_dir="./model_cache",
        low_cpu_mem_usage=True
    )
    print("✅ 模型加载完成！")

    # 清理内存
    gc.collect()
    torch.cuda.empty_cache()

except Exception as e:
    print(f"❌ 加载DeepSeek V3失败: {str(e)}")
    print("\n🔄 尝试加载较小的DeepSeek模型...")

    # 备选方案：使用较小的DeepSeek模型
    model_name = "deepseek-ai/deepseek-coder-6.7b-instruct"
    print(f"📥 正在加载备选模型: {model_name}")

    tokenizer = AutoTokenizer.from_pretrained(
        model_name,
        trust_remote_code=True,
        cache_dir="./model_cache"
    )

    model = AutoModelForCausalLM.from_pretrained(
        model_name,
        quantization_config=quantization_config,
        device_map="auto",
        trust_remote_code=True,
        torch_dtype=torch.float16,
        cache_dir="./model_cache",
        low_cpu_mem_usage=True
    )
    print("✅ 备选模型加载完成！")

def generate_response(prompt):
    """生成回答的函数，优化了内存使用"""
    try:
        # 构建输入格式
        messages = [{"role": "user", "content": prompt}]

        # 使用chat template
        if hasattr(tokenizer, 'apply_chat_template'):
            input_text = tokenizer.apply_chat_template(messages, tokenize=False, add_generation_prompt=True)
        else:
            # 备选格式
            input_text = f"User: {prompt}\n\nAssistant:"

        # 生成回答
        inputs = tokenizer(input_text, return_tensors="pt", truncation=True, max_length=2048)

        # 移动到GPU
        if torch.cuda.is_available():
            inputs = {k: v.to(model.device) for k, v in inputs.items()}

        # 生成参数优化
        with torch.no_grad():
            outputs = model.generate(
                **inputs,
                max_new_tokens=512,
                temperature=0.7,
                top_p=0.9,
                do_sample=True,
                pad_token_id=tokenizer.eos_token_id,
                repetition_penalty=1.1,
                no_repeat_ngram_size=3
            )

        # 解码回答
        response = tokenizer.decode(outputs[0], skip_special_tokens=True)

        # 提取新生成的部分
        if input_text in response:
            response = response.replace(input_text, "").strip()

        # 清理内存
        del inputs, outputs
        torch.cuda.empty_cache()

        return response

    except Exception as e:
        return f"生成回答时出错: {str(e)}"

def print_system_info():
    """打印系统信息"""
    print(f"\n📊 系统信息:")
    print(f"🔧 模型: {model_name}")
    if torch.cuda.is_available():
        print(f"💾 GPU显存使用: {torch.cuda.memory_allocated()/1024**3:.2f} GB / {torch.cuda.get_device_properties(0).total_memory/1024**3:.1f} GB")
        print(f"🔥 GPU利用率: {torch.cuda.utilization()}%")

# 交互式聊天循环
print("\n" + "="*50)
print("🤖 DeepSeek V3 Chat Bot 已启动!")
print("💡 输入 'quit' 退出，输入 'info' 查看系统信息")
print("="*50)

print_system_info()

while True:
    try:
        user_input = input("\n👤 用户: ").strip()

        if user_input.lower() in ['quit', 'exit', '退出']:
            print("👋 再见！")
            break
        elif user_input.lower() == 'info':
            print_system_info()
            continue
        elif not user_input:
            print("⚠️  请输入有效的问题")
            continue

        print("🤔 DeepSeek正在思考...")
        response = generate_response(user_input)
        print(f"\n🤖 DeepSeek: {response}")

    except KeyboardInterrupt:
        print("\n\n👋 用户中断，再见！")
        break
    except Exception as e:
        print(f"\n❌ 发生错误: {str(e)}")
        continue
