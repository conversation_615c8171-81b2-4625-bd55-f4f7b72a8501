#!/usr/bin/env python3
"""
DeepSeek 启动脚本
简化版本，直接启动聊天功能
"""

import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
import os

def main():
    print("🚀 DeepSeek 启动中...")
    print("=" * 50)
    
    # 检查环境
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    
    # 创建缓存目录
    os.makedirs("./model_cache", exist_ok=True)
    
    # 选择模型
    if torch.cuda.is_available():
        model_name = "deepseek-ai/deepseek-coder-1.3b-instruct"
        print(f"🎮 使用GPU模式，加载模型: {model_name}")
    else:
        model_name = "deepseek-ai/deepseek-coder-1.3b-instruct"
        print(f"💻 使用CPU模式，加载模型: {model_name}")
    
    try:
        print("\n📥 加载tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(
            model_name,
            trust_remote_code=True,
            cache_dir="./model_cache"
        )
        print("✅ Tokenizer加载完成")
        
        print("\n📥 加载模型...")
        model = AutoModelForCausalLM.from_pretrained(
            model_name,
            trust_remote_code=True,
            torch_dtype=torch.float32,
            cache_dir="./model_cache",
            low_cpu_mem_usage=True
        )
        print("✅ 模型加载完成")
        
        # 简单的聊天循环
        print("\n" + "="*50)
        print("🤖 DeepSeek 聊天已启动!")
        print("💡 输入 'quit' 退出")
        print("="*50)
        
        while True:
            try:
                user_input = input("\n👤 你: ").strip()
                
                if user_input.lower() in ['quit', 'exit', '退出']:
                    print("👋 再见!")
                    break
                
                if not user_input:
                    continue
                
                print("🤔 DeepSeek思考中...")
                
                # 构建输入
                messages = [{"role": "user", "content": user_input}]
                
                # 使用chat template
                if hasattr(tokenizer, 'apply_chat_template'):
                    input_text = tokenizer.apply_chat_template(
                        messages, 
                        tokenize=False, 
                        add_generation_prompt=True
                    )
                else:
                    input_text = f"User: {user_input}\n\nAssistant:"
                
                # 生成回答
                inputs = tokenizer(input_text, return_tensors="pt")
                
                with torch.no_grad():
                    outputs = model.generate(
                        **inputs,
                        max_new_tokens=256,
                        temperature=0.7,
                        do_sample=True,
                        pad_token_id=tokenizer.eos_token_id
                    )
                
                # 解码回答
                response = tokenizer.decode(outputs[0], skip_special_tokens=True)
                
                # 提取新生成的部分
                if input_text in response:
                    response = response.replace(input_text, "").strip()
                
                print(f"\n🤖 DeepSeek: {response}")
                
            except KeyboardInterrupt:
                print("\n\n👋 用户中断，再见!")
                break
            except Exception as e:
                print(f"\n❌ 生成回答时出错: {str(e)}")
                continue
    
    except Exception as e:
        print(f"❌ 模型加载失败: {str(e)}")
        print("💡 请检查网络连接或尝试其他模型")

if __name__ == "__main__":
    main()
