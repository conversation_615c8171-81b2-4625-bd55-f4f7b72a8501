from transformers import AutoModelForCausalLM, AutoTokenizer, logging
import torch
import os
import sys

# 设置详细的日志
logging.set_verbosity_info()
logging.enable_explicit_format()

# 创建缓存目录
cache_dir = "./model_cache"
os.makedirs(cache_dir, exist_ok=True)

print("开始初始化模型和分词器...")
print(f"缓存目录: {os.path.abspath(cache_dir)}")

# 初始化模型和分词器
model_name = "deepseek-ai/deepseek-coder-6.7b-instruct"  # 使用较小的6.7B模型
print(f"\n正在下载模型: {model_name}")
print("1. 下载tokenizer...")

try:
    tokenizer = AutoTokenizer.from_pretrained(
        model_name,
        trust_remote_code=True,
        cache_dir=cache_dir,
        resume_download=True
    )
    print("Tokenizer下载完成！")

    print("\n2. 下载模型...")
    model = AutoModelForCausalLM.from_pretrained(
        model_name,
        torch_dtype=torch.float16,
        device_map="auto",
        trust_remote_code=True,
        cache_dir=cache_dir,
        resume_download=True
    )
    print("模型下载完成！")

except Exception as e:
    print(f"错误: {str(e)}")
    sys.exit(1)

def generate_response(prompt):
    # 构建输入格式
    messages = [{"role": "user", "content": prompt}]
    input_text = tokenizer.apply_chat_template(messages, tokenize=False)
    
    # 生成回答
    inputs = tokenizer(input_text, return_tensors="pt").to(model.device)
    outputs = model.generate(
        **inputs,
        max_new_tokens=512,
        temperature=0.7,
        num_return_sequences=1,
        do_sample=True,
        pad_token_id=tokenizer.eos_token_id
    )
    
    response = tokenizer.decode(outputs[0], skip_special_tokens=True)
    return response

# 交互式聊天循环
print("\nDeepSeek Chat Bot 已启动 (输入 'quit' 退出)")
while True:
    user_input = input("\n用户: ")
    if user_input.lower() == 'quit':
        break
    
    try:
        response = generate_response(user_input)
        print("\nDeepSeek:", response)
    except Exception as e:
        print(f"生成回答时出错: {str(e)}")
