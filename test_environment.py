#!/usr/bin/env python3
"""
环境测试脚本
检查Python环境和依赖是否正确安装
"""

import sys
import platform

def test_basic_python():
    """测试基础Python环境"""
    print("🐍 Python环境测试")
    print("=" * 40)
    print(f"Python版本: {sys.version}")
    print(f"平台: {platform.platform()}")
    print(f"架构: {platform.architecture()}")
    print()

def test_pytorch():
    """测试PyTorch"""
    print("🔥 PyTorch测试")
    print("=" * 40)
    try:
        import torch
        print(f"✅ PyTorch版本: {torch.__version__}")
        print(f"✅ CUDA可用: {torch.cuda.is_available()}")
        
        if torch.cuda.is_available():
            print(f"✅ CUDA版本: {torch.version.cuda}")
            print(f"✅ GPU数量: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                gpu_name = torch.cuda.get_device_name(i)
                gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
                print(f"✅ GPU {i}: {gpu_name} ({gpu_memory:.1f} GB)")
        else:
            print("⚠️  CUDA不可用，将使用CPU")
    except ImportError:
        print("❌ PyTorch未安装")
        print("💡 安装命令: pip install torch")
    print()

def test_transformers():
    """测试Transformers"""
    print("🤗 Transformers测试")
    print("=" * 40)
    try:
        import transformers
        print(f"✅ Transformers版本: {transformers.__version__}")
    except ImportError:
        print("❌ Transformers未安装")
        print("💡 安装命令: pip install transformers")
    print()

def test_optional_deps():
    """测试可选依赖"""
    print("📦 可选依赖测试")
    print("=" * 40)
    
    # 测试量化库
    try:
        import bitsandbytes
        print(f"✅ BitsAndBytes版本: {bitsandbytes.__version__}")
    except ImportError:
        print("❌ BitsAndBytes未安装 (量化功能不可用)")
        print("💡 安装命令: pip install bitsandbytes")
    
    # 测试加速库
    try:
        import accelerate
        print(f"✅ Accelerate版本: {accelerate.__version__}")
    except ImportError:
        print("❌ Accelerate未安装")
        print("💡 安装命令: pip install accelerate")
    
    # 测试Gradio
    try:
        import gradio
        print(f"✅ Gradio版本: {gradio.__version__}")
    except ImportError:
        print("❌ Gradio未安装 (Web界面不可用)")
        print("💡 安装命令: pip install gradio")
    
    print()

def test_gpu_memory():
    """测试GPU内存"""
    print("💾 GPU内存测试")
    print("=" * 40)
    try:
        import torch
        if torch.cuda.is_available():
            for i in range(torch.cuda.device_count()):
                total_memory = torch.cuda.get_device_properties(i).total_memory
                allocated_memory = torch.cuda.memory_allocated(i)
                cached_memory = torch.cuda.memory_reserved(i)
                
                print(f"GPU {i}:")
                print(f"  总内存: {total_memory / 1024**3:.2f} GB")
                print(f"  已分配: {allocated_memory / 1024**3:.2f} GB")
                print(f"  已缓存: {cached_memory / 1024**3:.2f} GB")
                print(f"  可用: {(total_memory - allocated_memory) / 1024**3:.2f} GB")
        else:
            print("⚠️  无GPU可用")
    except ImportError:
        print("❌ PyTorch未安装，无法检测GPU")
    print()

def recommend_installation():
    """推荐安装步骤"""
    print("💡 推荐安装步骤")
    print("=" * 40)
    print("1. 安装PyTorch (CUDA版本):")
    print("   pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121")
    print()
    print("2. 安装基础依赖:")
    print("   pip install transformers accelerate bitsandbytes")
    print()
    print("3. 安装Web界面 (可选):")
    print("   pip install gradio")
    print()
    print("4. 安装高性能推理框架 (可选):")
    print("   pip install sglang[all]")
    print("   或")
    print("   pip install vllm")
    print()

def main():
    """主函数"""
    print("🔍 DeepSeek V3 环境检测")
    print("=" * 50)
    print()
    
    test_basic_python()
    test_pytorch()
    test_transformers()
    test_optional_deps()
    test_gpu_memory()
    recommend_installation()
    
    print("✅ 环境检测完成!")
    print("💡 如果有缺失的依赖，请按照上述建议安装")

if __name__ == "__main__":
    main()
