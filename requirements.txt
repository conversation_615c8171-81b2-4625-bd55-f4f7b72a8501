# DeepSeek V3 本地部署依赖

# 基础依赖
torch>=2.0.0
transformers>=4.36.0
tokenizers>=0.15.0
accelerate>=0.24.0
bitsandbytes>=0.41.0

# 量化和优化
optimum>=1.14.0
auto-gptq>=0.5.0

# 推理框架 (可选，根据需要安装)
# sglang[all]  # 高性能推理框架
# vllm>=0.6.6  # 另一个高性能推理框架

# 工具库
numpy>=1.24.0
requests>=2.28.0
tqdm>=4.64.0
psutil>=5.9.0

# 开发和调试
ipython>=8.0.0
jupyter>=1.0.0

# 可选：如果需要使用Gradio界面
# gradio>=4.0.0
# fastapi>=0.100.0
# uvicorn>=0.23.0
