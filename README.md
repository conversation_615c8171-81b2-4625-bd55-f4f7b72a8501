# 🚀 DeepSeek V3 本地部署指南

本项目提供了在RTX 3090等消费级GPU上部署DeepSeek V3模型的完整解决方案。

## 📋 系统要求

### 硬件要求
- **GPU**: NVIDIA RTX 3090 (24GB) 或更高
- **内存**: 32GB+ RAM (推荐)
- **存储**: 100GB+ 可用空间

### 软件要求
- Python 3.8+
- CUDA 11.8+ 或 12.1+
- PyTorch 2.0+

## 🛠️ 安装步骤

### 1. 克隆项目
```bash
git clone <your-repo-url>
cd deepseekV3
```

### 2. 创建虚拟环境
```bash
python -m venv venv
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate
```

### 3. 安装基础依赖
```bash
pip install -r requirements.txt
```

### 4. 安装推理框架 (可选)

#### 选项A: SGLang (推荐，性能最佳)
```bash
pip install sglang[all]
pip install flashinfer -i https://flashinfer.ai/whl/cu121/torch2.4/
```

#### 选项B: vLLM
```bash
pip install vllm
```

#### 选项C: 仅使用Transformers
```bash
# 已包含在requirements.txt中
```

## 🚀 使用方法

### 方法1: 命令行聊天 (简单)
```bash
python chat.py
```

### 方法2: Web界面 (推荐)
```bash
# 安装Gradio
pip install gradio

# 启动Web界面
python web_interface.py
```
然后访问: http://localhost:7860

### 方法3: 高性能服务器
```bash
# 使用SGLang
python deepseek_v3_advanced.py --framework sglang --install-deps

# 使用vLLM
python deepseek_v3_advanced.py --framework vllm --install-deps
```

## 📊 性能优化

### RTX 3090 优化配置

1. **4bit量化**: 减少显存使用至约12-16GB
2. **FP8推理**: 使用SGLang获得最佳性能
3. **序列长度限制**: 设置max_length=2048以节省显存

### 显存使用情况
- **DeepSeek V3 + 4bit量化**: ~12-16GB
- **DeepSeek Coder 6.7B + 4bit**: ~4-6GB
- **DeepSeek Coder 33B + 4bit**: ~8-12GB

## 🔧 配置选项

### 模型选择
- `deepseek-ai/DeepSeek-V3`: 最新的671B参数模型
- `deepseek-ai/deepseek-coder-6.7b-instruct`: 轻量级代码模型
- `deepseek-ai/deepseek-coder-33b-instruct`: 中等规模代码模型

### 量化选项
- **4bit NF4**: 最佳显存效率
- **8bit**: 平衡性能和显存
- **FP16**: 最佳性能，需要更多显存

## 🐛 故障排除

### 常见问题

#### 1. 显存不足
```
OutOfMemoryError: CUDA out of memory
```
**解决方案**:
- 启用4bit量化
- 减少max_length参数
- 使用较小的模型

#### 2. 模型下载失败
```
ConnectionError: Failed to download model
```
**解决方案**:
- 检查网络连接
- 使用镜像源
- 手动下载模型文件

#### 3. 依赖冲突
```
ImportError: No module named 'xxx'
```
**解决方案**:
- 重新创建虚拟环境
- 按顺序安装依赖
- 检查CUDA版本兼容性

### 性能调优

#### 提升推理速度
1. 使用SGLang或vLLM
2. 启用FP8量化
3. 调整batch_size
4. 使用较短的序列长度

#### 减少显存使用
1. 启用4bit量化
2. 使用gradient_checkpointing
3. 减少cache大小
4. 选择较小的模型

## 📈 基准测试

### RTX 3090 性能数据

| 模型 | 量化 | 显存使用 | 推理速度 |
|------|------|----------|----------|
| DeepSeek V3 | 4bit | ~14GB | 2-3 tokens/s |
| DeepSeek Coder 33B | 4bit | ~10GB | 5-8 tokens/s |
| DeepSeek Coder 6.7B | 4bit | ~5GB | 15-25 tokens/s |

## 🔗 相关链接

- [DeepSeek V3 官方页面](https://huggingface.co/deepseek-ai/DeepSeek-V3)
- [SGLang 文档](https://github.com/sgl-project/sglang)
- [vLLM 文档](https://docs.vllm.ai/)
- [Transformers 文档](https://huggingface.co/docs/transformers)

## 📝 许可证

本项目遵循MIT许可证。DeepSeek V3模型遵循其官方许可证。

## 🤝 贡献

欢迎提交Issue和Pull Request！

## ⚠️ 注意事项

1. 首次运行会下载大量模型文件（~100GB+）
2. 确保有足够的存储空间和网络带宽
3. 建议在稳定的电源环境下运行
4. 定期清理模型缓存以释放空间
