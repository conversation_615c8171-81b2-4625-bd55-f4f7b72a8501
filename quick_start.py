#!/usr/bin/env python3
"""
DeepSeek V3 快速启动脚本
自动检测环境并选择最佳配置
"""

import os
import sys
import subprocess
import torch
import platform
from pathlib import Path

def print_banner():
    """打印启动横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                    🚀 DeepSeek V3 快速启动                    ║
    ║                                                              ║
    ║  自动检测您的硬件配置并推荐最佳部署方案                        ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_system():
    """检查系统配置"""
    print("🔍 检查系统配置...")
    
    # 操作系统
    os_name = platform.system()
    print(f"💻 操作系统: {os_name} {platform.release()}")
    
    # Python版本
    python_version = sys.version.split()[0]
    print(f"🐍 Python版本: {python_version}")
    
    # CUDA检查
    cuda_available = torch.cuda.is_available()
    if cuda_available:
        gpu_count = torch.cuda.device_count()
        gpu_name = torch.cuda.get_device_name(0)
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        
        print(f"🎮 GPU: {gpu_name}")
        print(f"💾 显存: {gpu_memory:.1f} GB")
        print(f"🔢 GPU数量: {gpu_count}")
        
        return {
            'has_gpu': True,
            'gpu_memory': gpu_memory,
            'gpu_name': gpu_name,
            'gpu_count': gpu_count
        }
    else:
        print("❌ 未检测到CUDA GPU")
        return {'has_gpu': False}

def recommend_config(system_info):
    """推荐配置"""
    print("\n💡 推荐配置:")
    
    if not system_info['has_gpu']:
        print("⚠️  建议使用CPU模式，性能较慢")
        return {
            'model': 'deepseek-coder-6.7b',
            'quantization': True,
            'framework': 'transformers',
            'interface': 'cli'
        }
    
    gpu_memory = system_info['gpu_memory']
    
    if gpu_memory >= 20:  # RTX 3090, 4090等
        print("✅ 显存充足，可以运行DeepSeek V3")
        return {
            'model': 'deepseek-v3',
            'quantization': True,
            'framework': 'sglang',
            'interface': 'web'
        }
    elif gpu_memory >= 12:  # RTX 3060 12GB, 4060 Ti等
        print("⚠️  显存中等，建议使用中等规模模型")
        return {
            'model': 'deepseek-coder-33b',
            'quantization': True,
            'framework': 'transformers',
            'interface': 'web'
        }
    else:  # RTX 3060 8GB等
        print("⚠️  显存较少，建议使用小规模模型")
        return {
            'model': 'deepseek-coder-6.7b',
            'quantization': True,
            'framework': 'transformers',
            'interface': 'cli'
        }

def install_dependencies(config):
    """安装依赖"""
    print("\n📦 安装依赖...")
    
    # 基础依赖
    basic_deps = [
        "torch>=2.0.0",
        "transformers>=4.36.0",
        "accelerate>=0.24.0",
        "bitsandbytes>=0.41.0"
    ]
    
    # 根据框架添加依赖
    if config['framework'] == 'sglang':
        basic_deps.extend([
            "sglang[all]",
            "flashinfer"
        ])
    elif config['framework'] == 'vllm':
        basic_deps.append("vllm")
    
    # 根据界面添加依赖
    if config['interface'] == 'web':
        basic_deps.append("gradio")
    
    for dep in basic_deps:
        try:
            print(f"📥 安装 {dep}...")
            subprocess.run([sys.executable, "-m", "pip", "install", dep], 
                         check=True, capture_output=True)
            print(f"✅ {dep} 安装成功")
        except subprocess.CalledProcessError:
            print(f"❌ {dep} 安装失败，请手动安装")

def create_config_file(config):
    """创建配置文件"""
    config_content = f"""# DeepSeek V3 配置文件
# 由快速启动脚本自动生成

MODEL_NAME = "{config['model']}"
USE_QUANTIZATION = {config['quantization']}
FRAMEWORK = "{config['framework']}"
INTERFACE = "{config['interface']}"

# 生成参数
TEMPERATURE = 0.7
MAX_TOKENS = 512
TOP_P = 0.9

# 系统参数
CACHE_DIR = "./model_cache"
MAX_MEMORY_GB = 20
"""
    
    with open("config.py", "w", encoding="utf-8") as f:
        f.write(config_content)
    
    print("✅ 配置文件已创建: config.py")

def launch_application(config):
    """启动应用"""
    print(f"\n🚀 启动应用 (模式: {config['interface']})...")
    
    if config['interface'] == 'web':
        print("🌐 启动Web界面...")
        print("📍 访问地址: http://localhost:7860")
        try:
            subprocess.run([sys.executable, "web_interface.py"])
        except KeyboardInterrupt:
            print("\n👋 用户中断，退出程序")
    
    elif config['interface'] == 'cli':
        print("💻 启动命令行界面...")
        try:
            subprocess.run([sys.executable, "chat.py"])
        except KeyboardInterrupt:
            print("\n👋 用户中断，退出程序")
    
    elif config['framework'] in ['sglang', 'vllm']:
        print(f"🔧 启动{config['framework']}服务器...")
        try:
            subprocess.run([sys.executable, "deepseek_v3_advanced.py", 
                          "--framework", config['framework']])
        except KeyboardInterrupt:
            print("\n👋 用户中断，退出程序")

def main():
    """主函数"""
    print_banner()
    
    # 检查系统
    system_info = check_system()
    
    # 推荐配置
    config = recommend_config(system_info)
    
    # 显示推荐配置
    print(f"\n📋 推荐配置:")
    print(f"🤖 模型: {config['model']}")
    print(f"⚡ 量化: {'启用' if config['quantization'] else '禁用'}")
    print(f"🔧 框架: {config['framework']}")
    print(f"🖥️  界面: {config['interface']}")
    
    # 询问用户确认
    choice = input("\n❓ 是否使用推荐配置? (y/n): ").lower().strip()
    
    if choice not in ['y', 'yes', '是', '']:
        print("🔧 请手动配置...")
        
        # 手动选择模型
        models = {
            '1': 'deepseek-v3',
            '2': 'deepseek-coder-33b', 
            '3': 'deepseek-coder-6.7b'
        }
        
        print("\n选择模型:")
        print("1. DeepSeek V3 (671B, 需要24GB+显存)")
        print("2. DeepSeek Coder 33B (需要12GB+显存)")
        print("3. DeepSeek Coder 6.7B (需要6GB+显存)")
        
        model_choice = input("请选择 (1-3): ").strip()
        if model_choice in models:
            config['model'] = models[model_choice]
        
        # 手动选择界面
        interfaces = {
            '1': 'web',
            '2': 'cli'
        }
        
        print("\n选择界面:")
        print("1. Web界面 (推荐)")
        print("2. 命令行界面")
        
        interface_choice = input("请选择 (1-2): ").strip()
        if interface_choice in interfaces:
            config['interface'] = interfaces[interface_choice]
    
    # 创建配置文件
    create_config_file(config)
    
    # 询问是否安装依赖
    install_choice = input("\n❓ 是否自动安装依赖? (y/n): ").lower().strip()
    if install_choice in ['y', 'yes', '是', '']:
        install_dependencies(config)
    
    # 启动应用
    launch_choice = input("\n❓ 是否立即启动应用? (y/n): ").lower().strip()
    if launch_choice in ['y', 'yes', '是', '']:
        launch_application(config)
    else:
        print("\n📝 配置完成！")
        print("🚀 手动启动命令:")
        if config['interface'] == 'web':
            print("   python web_interface.py")
        elif config['interface'] == 'cli':
            print("   python chat.py")
        else:
            print(f"   python deepseek_v3_advanced.py --framework {config['framework']}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，退出程序")
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")
        print("请检查系统配置或手动运行相关脚本")
